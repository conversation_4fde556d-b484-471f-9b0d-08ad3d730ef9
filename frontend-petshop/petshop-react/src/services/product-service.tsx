import axios from "axios";
import type { AxiosInstance } from "axios";

interface WeightProduct {
  unit: string;
  weightType: {
    value: number;
  };
}

interface ProductResponse {
  data: {
    data: any;
    elements?: any;
  };
}

interface FilterParams {
  query?: string;
  organic?: boolean;
  minPrice?: number;
  maxPrice?: number;
  categoryId?: number;
  brand?: string;
  origin?: string;
  page?: number;
  size?: number;
}

class ProductService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: "http://localhost:8888/api/v1/products",
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: false,
    });
  }

  async getProductsByCategory(categoryId: number, page: number = 1, size: number = 8): Promise<any> {
    try {
      const response: ProductResponse = await this.api.get(`/category/${categoryId}`, {
        params: { page, size },
      });
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching products by category:", error);
      throw error;
    }
  }

  async getProducts(page: number = 1, size: number = 6): Promise<any> {
    try {
      const response: ProductResponse = await this.api.get("", { params: { page, size } });
      console.log(response)
      return response.data.data.elements;
    } catch (error: any) {
      console.error("Error fetching products by category:", error);
      throw error;
    }
  }

  async getFilteredProducts(params: FilterParams): Promise<any> {
    try {
      const response: ProductResponse = await this.api.get("/filter", { params });
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching filtered products:", error);
      throw error;
    }
  }

  async getProductById(id: string): Promise<any> {
    try {
      const response: ProductResponse = await this.api.get(`/${id}`);
      console.log(response)
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching product by id:", error);
      throw error;
    }
  }

  getMaxPrice(weightProducts: WeightProduct[], price: number): number {
    let maxValue = 0;
    weightProducts.forEach((weightProduct) => {
      let value = 0;
      if (weightProduct.unit === "g") {
        value = (price * weightProduct.weightType.value) / 1000;
      } else {
        value = price * weightProduct.weightType.value;
      }
      if (value > maxValue) {
        maxValue = value;
      }
    });
    return maxValue;
  }

  getMinPrice(weightProducts: WeightProduct[], price: number): number {
    let minValue = 9999999999;
    weightProducts.forEach((weightProduct) => {
      let value = 0;
      if (weightProduct.unit === "g") {
        value = (price * weightProduct.weightType.value) / 1000;
      } else {
        value = price * weightProduct.weightType.value;
      }
      if (value < minValue) {
        minValue = value;
      }
    });
    return minValue;
  }
}

export default new ProductService();