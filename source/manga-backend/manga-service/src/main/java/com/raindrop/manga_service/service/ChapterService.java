package com.raindrop.manga_service.service;

import com.raindrop.manga_service.dto.request.ChapterRequest;
import com.raindrop.manga_service.dto.response.ApiResponse;
import com.raindrop.manga_service.dto.response.ChapterInfoResponse;
import com.raindrop.manga_service.dto.response.ChapterResponse;
import com.raindrop.manga_service.dto.response.FileDataResponse;
import com.raindrop.manga_service.dto.response.PageResponse;
import com.raindrop.manga_service.entity.Chapter;
import com.raindrop.manga_service.entity.Manga;
import com.raindrop.manga_service.entity.Page;
import com.raindrop.manga_service.enums.ErrorCode;
import com.raindrop.manga_service.exception.AppException;
import com.raindrop.manga_service.mapper.ChapterMapper;
import com.raindrop.manga_service.repository.ChapterRepository;
import com.raindrop.manga_service.repository.MangaRepository;
import com.raindrop.manga_service.repository.PageRepository;
import com.raindrop.manga_service.repository.httpclient.UploadClient;
import jakarta.transaction.Transactional;
import com.raindrop.manga_service.kafka.NewChapterEventProducer;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;

@Service
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@Slf4j
public class ChapterService {
    ChapterRepository chapterRepository;
    ChapterMapper chapterMapper;
    UploadClient uploadClient;
    MangaRepository mangaRepository;
    PageRepository pageRepository;
    NewChapterEventProducer newChapterEventProducer;

    // ==================== COMMON RESPONSE BUILDING METHODS ====================

    /**
     * Build ChapterResponse từ Chapter entity (tái sử dụng logic chung)
     * @param chapter Chapter entity
     * @return ChapterResponse đã được build
     */
    private ChapterResponse buildChapterResponse(Chapter chapter) {
        return ChapterResponse.builder()
                .id(chapter.getId())
                .chapterNumber(chapter.getChapterNumber())
                .title(chapter.getTitle())
                .mangaTitle(chapter.getManga().getTitle())
                .mangaId(chapter.getManga().getId())
                .pages(buildPageResponses(chapter.getPages()))
                .views(chapter.getViews())
                .comments(chapter.getComments())
                .updatedAt(chapter.getUpdatedAt())
                .build();
    }

    /**
     * Build danh sách PageResponse từ danh sách Page entities
     * @param pages Danh sách Page entities
     * @return Danh sách PageResponse đã được sắp xếp
     */
    private List<PageResponse> buildPageResponses(List<Page> pages) {
        if (pages == null || pages.isEmpty()) {
            return Collections.emptyList();
        }

        return pages.stream()
                .sorted(Comparator.comparingInt(Page::getIndex))
                .map(this::buildPageResponse)
                .toList();
    }

    /**
     * Build PageResponse từ Page entity
     * @param page Page entity
     * @return PageResponse
     */
    private PageResponse buildPageResponse(Page page) {
        return PageResponse.builder()
                .index(page.getIndex())
                .pageUrl(page.getPageUrl())
                .build();
    }

    /**
     * Build ChapterResponse cho create/update operations (với pages đã sorted)
     * @param chapter Chapter entity
     * @return ChapterResponse với pages đã được sắp xếp
     */
    private ChapterResponse buildChapterResponseForModification(Chapter chapter) {
        return ChapterResponse.builder()
                .id(chapter.getId())
                .title(chapter.getTitle())
                .chapterNumber(chapter.getChapterNumber())
                .mangaId(chapter.getManga().getId())
                .pages(buildPageResponses(chapter.getPages()))
                .updatedAt(chapter.getUpdatedAt())
                .build();
    }

    /**
     * Build ChapterInfoResponse từ Chapter entity
     * @param chapter Chapter entity
     * @return ChapterInfoResponse
     */
    private ChapterInfoResponse buildChapterInfoResponse(Chapter chapter) {
        return ChapterInfoResponse.builder()
                .id(chapter.getId())
                .chapterNumber(String.valueOf(chapter.getChapterNumber()))
                .title(chapter.getTitle())
                .mangaId(chapter.getManga().getId())
                .mangaTitle(chapter.getManga().getTitle())
                .build();
    }

    // ==================== OPTIMIZED PAGE OPERATIONS ====================

    /**
     * Tìm page theo chapter ID và index (tối ưu)
     * @param chapterId ID của chapter
     * @param pageIndex Index của page
     * @return Page nếu tìm thấy
     */
    private Page findPageByIndex(String chapterId, int pageIndex) {
        return pageRepository.findByChapterIdAndIndex(chapterId, pageIndex)
                .orElseThrow(() -> new AppException(ErrorCode.PAGE_NOT_FOUND));
    }

    /**
     * Validate page index trong chapter
     * @param chapter Chapter entity
     * @param pageIndex Index cần validate
     */
    private void validatePageIndex(Chapter chapter, int pageIndex) {
        List<Page> pages = chapter.getPages();
        if (pages == null || pages.isEmpty()) {
            throw new AppException(ErrorCode.CHAPTER_NO_PAGES);
        }

        if (pageIndex < 0 || pageIndex >= pages.size()) {
            throw new AppException(ErrorCode.PAGE_INDEX_OUT_OF_RANGE);
        }
    }

    /**
     * Batch update page indexes sau khi xóa page
     * @param pages Danh sách pages cần update
     * @param deletedIndex Index của page đã bị xóa
     */
    private void batchUpdatePageIndexesAfterDeletion(List<Page> pages, int deletedIndex) {
        List<Page> pagesToUpdate = pages.stream()
                .filter(page -> page.getIndex() > deletedIndex)
                .toList();

        if (!pagesToUpdate.isEmpty()) {
            // Update indexes in batch
            pagesToUpdate.forEach(page -> page.setIndex(page.getIndex() - 1));
            pageRepository.saveAll(pagesToUpdate);
            log.info("Batch updated {} page indexes after deletion", pagesToUpdate.size());
        }
    }

    /**
     * Upload multiple pages với error handling
     * @param files Danh sách files cần upload
     * @param chapter Chapter để gán pages
     * @param startIndex Index bắt đầu
     * @param authHeader Authorization header
     * @return Danh sách pages đã được tạo
     */
    private List<Page> uploadMultiplePages(List<MultipartFile> files, Chapter chapter, int startIndex, String authHeader) {
        List<Page> uploadedPages = new ArrayList<>();

        for (int i = 0; i < files.size(); i++) {
            MultipartFile file = files.get(i);
            try {
                ApiResponse<FileDataResponse> apiResponse = uploadClient.uploadMedia(authHeader, file);
                Page page = Page.builder()
                        .index(startIndex + i)
                        .pageUrl(apiResponse.getResult().getFileName())
                        .chapter(chapter)
                        .build();
                page = pageRepository.save(page);
                uploadedPages.add(page);

                log.debug("Uploaded page {} for chapter {}", startIndex + i, chapter.getId());
            } catch (Exception e) {
                log.error("Error uploading file [{}]: {}", i, e.getMessage());
                // Cleanup uploaded pages if error occurs
                cleanupUploadedPages(uploadedPages, authHeader);
                throw new AppException(ErrorCode.PAGE_UPLOAD_FAILED);
            }
        }

        return uploadedPages;
    }

    /**
     * Cleanup uploaded pages khi có lỗi
     * @param uploadedPages Danh sách pages đã upload
     * @param authHeader Authorization header
     */
    private void cleanupUploadedPages(List<Page> uploadedPages, String authHeader) {
        for (Page page : uploadedPages) {
            try {
                if (page.getPageUrl() != null) {
                    uploadClient.deleteMedia(authHeader, page.getPageUrl());
                }
                pageRepository.delete(page);
            } catch (Exception e) {
                log.warn("Failed to cleanup page: {}", e.getMessage());
            }
        }
    }

    @Transactional
    public ChapterResponse createChapter(ChapterRequest request) {
        if (request.getPages() == null || request.getPages().isEmpty()) {
            throw new AppException(ErrorCode.CHAPTER_NO_PAGES);
        }

        Manga manga = mangaRepository.findById(request.getMangaId())
                .orElseThrow(() -> new AppException(ErrorCode.MANGA_NOT_FOUND));

        // **Tạo Chapter trước để có ID**
        Chapter chapter = Chapter.builder()
                .chapterNumber(request.getChapterNumber())
                .title(request.getTitle())
                .manga(manga)
                .build();
        chapter = chapterRepository.save(chapter);

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        var header = attributes.getRequest().getHeader("Authorization");

        // **Tạo và lưu các Page sử dụng helper method**
        List<Page> pages = uploadMultiplePages(request.getPages(), chapter, 0, header);

        // **Cập nhật danh sách pages trong Chapter (đồng bộ hóa)**
        chapter.setPages(pages);
        chapterRepository.save(chapter); // Cập nhật Chapter với danh sách pages

        // Cập nhật ID chapter mới nhất của manga
        manga.setLastChapterId(chapter.getId());
        // Cập nhật thời gian thêm chapter mới nhất của manga
        manga.setLastChapterAddedAt(LocalDateTime.now());
        mangaRepository.save(manga);

        // Cập nhật tổng số lượt xem và comment của manga
//        mangaStatsService.updateMangaTotalViews(manga.getId());
//        mangaStatsService.updateMangaTotalComments(manga.getId());

        // Gửi sự kiện chapter mới để thông báo cho người dùng đã yêu thích truyện
        newChapterEventProducer.sendNewChapterEvent(
                manga.getId(),
                manga.getTitle(),
                chapter.getId(),
                chapter.getChapterNumber(),
                chapter.getTitle()
        );

        // **Tạo response sử dụng helper method**
        return buildChapterResponseForModification(chapter);
    }

    public ChapterResponse getChapterById(String id) {
        // Sử dụng eager loading để tránh N+1 problem
        Chapter chapter = chapterRepository.findByIdWithPagesAndManga(id)
                .orElseThrow(() -> new AppException(ErrorCode.CHAPTER_NOT_FOUND));
        return buildChapterResponse(chapter);
    }


    public org.springframework.data.domain.Page<ChapterResponse> getAllChapters(Pageable pageable) {
        org.springframework.data.domain.Page<Chapter> chapters = chapterRepository.findAllWithPagesAndManga(pageable);
        return chapters.map(this::buildChapterResponse);
    }

    /**
     * Tìm kiếm và lọc chapter theo manga với eager loading (tối ưu)
     */
    public org.springframework.data.domain.Page<ChapterResponse> searchAndFilterChapters(String mangaId, Pageable pageable) {
        log.info("Searching and filtering chapters with criteria - mangaId: {}", mangaId);

        // Xử lý trường hợp mangaId rỗng
        mangaId = (mangaId != null && !mangaId.trim().isEmpty()) ? mangaId.trim() : null;

        // Sử dụng eager loading để tránh N+1 problem
        org.springframework.data.domain.Page<Chapter> chapters = chapterRepository.searchAndFilterChaptersWithPages(mangaId, pageable);

        org.springframework.data.domain.Page<ChapterResponse> chapterResponsePage = chapters.map(this::buildChapterResponse);

        log.info("Found {} chapters matching the criteria", chapterResponsePage.getTotalElements());
        return chapterResponsePage;
    }

    /**
     * Lấy danh sách chapter của một manga với eager loading (tối ưu)
     */
    public List<ChapterResponse> getChaptersByMangaId(String mangaId) {
        log.info("Getting chapters for manga: {}", mangaId);

        // Kiểm tra manga có tồn tại không
        Manga manga = mangaRepository.findById(mangaId)
                .orElseThrow(() -> new AppException(ErrorCode.MANGA_NOT_FOUND));

        // Sử dụng eager loading để tránh N+1 problem
        List<Chapter> chapters = chapterRepository.findByMangaIdWithPages(mangaId);
        List<ChapterResponse> chapterResponses = chapters.stream()
                .map(this::buildChapterResponse)
                .toList();

        log.info("Retrieved {} chapters for manga: {}", chapterResponses.size(), mangaId);
        return chapterResponses;
    }

    /**
     * Lấy thông tin cơ bản của chapter
     * @param id ID của chapter
     * @return Thông tin cơ bản của chapter
     */
    public ChapterInfoResponse getChapterInfo(String id) {
        log.info("Getting basic info for chapter: {}", id);
        Chapter chapter = chapterRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.CHAPTER_NOT_FOUND));

        return buildChapterInfoResponse(chapter);
    }

    /**
     * Cập nhật chapter
     * @param id ID của chapter
     * @param request Thông tin cập nhật
     * @return Thông tin chapter sau khi cập nhật
     */
    @Transactional
    public ChapterResponse updateChapter(String id, ChapterRequest request) {
        log.info("Updating chapter: {}", id);

        // Kiểm tra chapter có tồn tại không
        Chapter chapter = chapterRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.CHAPTER_NOT_FOUND));

        // Cập nhật tiêu đề nếu có
        if (request.getTitle() != null && !request.getTitle().isEmpty()) {
            chapter.setTitle(request.getTitle());
        } else {
            log.info("No title provided or empty title, keeping existing title: {}", chapter.getTitle());
        }

        // Cập nhật danh sách trang nếu có
        if (request.getPages() != null && !request.getPages().isEmpty()) {
            // Lấy danh sách trang hiện tại
            List<Page> currentPages = chapter.getPages();
            if (currentPages == null) {
                // Nếu danh sách là null, khởi tạo một danh sách mới
                currentPages = new ArrayList<>();
                chapter.setPages(currentPages);
            }

            // Tính toán index bắt đầu cho các trang mới
            int startIndex = currentPages.isEmpty() ? 0 :
                            currentPages.stream()
                                .mapToInt(Page::getIndex)
                                .max()
                                .orElse(-1) + 1;

            log.info("Adding {} new pages starting from index {}", request.getPages().size(), startIndex);

            // Tạo các trang mới sử dụng helper method
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            var header = attributes.getRequest().getHeader("Authorization");

            List<Page> newPages = uploadMultiplePages(request.getPages(), chapter, startIndex, header);

            // Thêm trang mới vào danh sách trang của chapter
            chapter.getPages().addAll(newPages);
        }

        // Lưu chapter đã cập nhật
        chapter = chapterRepository.save(chapter);

        return buildChapterResponseForModification(chapter);
    }

    /**
     * Cập nhật một trang cụ thể trong chapter
     * @param id ID của chapter
     * @param pageIndex Vị trí của trang cần cập nhật
     * @param pageFile File ảnh mới
     * @return Thông tin chapter sau khi cập nhật
     */
    @Transactional
    public ChapterResponse updateChapterPage(String id, int pageIndex, MultipartFile pageFile) {
        log.info("Updating page at index {} for chapter: {}", pageIndex, id);

        // Kiểm tra chapter có tồn tại không
        Chapter chapter = chapterRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.CHAPTER_NOT_FOUND));

        // Validate page index
        validatePageIndex(chapter, pageIndex);

        // Tìm trang cần cập nhật sử dụng optimized query
        Page pageToUpdate = findPageByIndex(id, pageIndex);

        // Tải lên ảnh mới
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        var header = attributes.getRequest().getHeader("Authorization");

        try {
            ApiResponse<FileDataResponse> apiResponse = uploadClient.uploadMedia(header, pageFile);

            // Cập nhật URL của trang
            pageToUpdate.setPageUrl(apiResponse.getResult().getFileName());
            pageRepository.save(pageToUpdate);

            // Lưu chapter đã cập nhật
            chapter = chapterRepository.save(chapter);

            return buildChapterResponseForModification(chapter);
        } catch (Exception e) {
            log.error("Error uploading file for page index {}: {}", pageIndex, e.getMessage());
            throw new AppException(ErrorCode.PAGE_UPLOAD_FAILED);
        }
    }

    /**
     * Xóa một trang cụ thể trong chapter
     * @param id ID của chapter
     * @param pageIndex Vị trí của trang cần xóa
     * @return Thông tin chapter sau khi xóa trang
     */
    @Transactional
    public ChapterResponse deleteChapterPage(String id, int pageIndex) {
        log.info("Deleting page at index {} for chapter: {}", pageIndex, id);

        // Kiểm tra chapter có tồn tại không
        Chapter chapter = chapterRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.CHAPTER_NOT_FOUND));

        // Validate page index
        validatePageIndex(chapter, pageIndex);

        // Tìm trang cần xóa sử dụng optimized query
        Page pageToDelete = findPageByIndex(id, pageIndex);

        // Xóa trang
        List<Page> pages = chapter.getPages();
        pages.remove(pageToDelete);
        pageRepository.delete(pageToDelete);

        // Batch update indexes của các trang còn lại
        batchUpdatePageIndexesAfterDeletion(pages, pageIndex);

        // Lưu chapter đã cập nhật
        chapter = chapterRepository.save(chapter);

        return buildChapterResponseForModification(chapter);
    }

    /**
     * Xóa một chapter
     * @param id ID của chapter cần xóa
     */
    @Transactional
    public void deleteChapter(String id) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        var header = attributes.getRequest().getHeader("Authorization");
        Chapter chapter = chapterRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.CHAPTER_NOT_FOUND));

        // Lấy manga để cập nhật lại thông tin sau khi xóa chapter
        Manga manga = chapter.getManga();

        // Kiểm tra xem chapter đang xóa có phải là chapter mới nhất không
        boolean isLatestChapter = id.equals(manga.getLastChapterId());

        // Xóa tất cả các trang của chapter (batch operation)
        List<Page> pages = chapter.getPages();
        if (pages != null && !pages.isEmpty()) {
            // Batch delete files từ upload service
            for (Page page : pages) {
                try {
                    if (page.getPageUrl() != null && !page.getPageUrl().isEmpty()) {
                        uploadClient.deleteMedia(header, page.getPageUrl());
                    }
                } catch (Exception e) {
                    log.error("Error deleting page file: {}", e.getMessage(), e);
                    // Tiếp tục xóa chapter ngay cả khi không xóa được file
                }
            }

            // Batch delete pages từ database
            pageRepository.deleteByChapterId(id);
            log.info("Batch deleted {} pages for chapter {}", pages.size(), id);
        }

        // Xóa chapter từ database
        chapterRepository.delete(chapter);
        log.info("Chapter deleted successfully: {}", id);

        // Nếu chapter vừa xóa là chapter mới nhất, cập nhật lại thông tin manga
        if (isLatestChapter) {
            log.info("Deleted chapter was the latest chapter, updating manga info...");
            updateMangaLatestChapter(manga);
        }
    }

    /**
     * Cập nhật thông tin chapter mới nhất của manga sau khi xóa chapter
     * @param manga Manga cần cập nhật
     */
    private void updateMangaLatestChapter(Manga manga) {
        // Lấy danh sách chapter còn lại của manga, sắp xếp theo chapterNumber giảm dần
        List<Chapter> remainingChapters = chapterRepository.findByMangaIdOrderByChapterNumberDesc(manga.getId());

        if (remainingChapters.isEmpty()) {
            // Nếu không còn chapter nào, set lastChapterId và lastChapterAddedAt về null
            manga.setLastChapterId(null);
            manga.setLastChapterAddedAt(null);
            log.info("No chapters remaining for manga: {}, set lastChapterId to null", manga.getId());
        } else {
            // Lấy chapter có chapterNumber cao nhất (chapter đầu tiên trong danh sách đã sắp xếp)
            Chapter newLatestChapter = remainingChapters.get(0);
            manga.setLastChapterId(newLatestChapter.getId());
            manga.setLastChapterAddedAt(newLatestChapter.getCreatedAt());
            log.info("Updated latest chapter for manga: {} to chapter: {} (number: {})",
                    manga.getId(), newLatestChapter.getId(), newLatestChapter.getChapterNumber());
        }

        // Lưu thông tin manga đã cập nhật
        mangaRepository.save(manga);
    }
}
