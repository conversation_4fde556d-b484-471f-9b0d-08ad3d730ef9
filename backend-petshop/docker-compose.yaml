version: '3.8'

services:
  kafka:
    image: 'bitnami/kafka:3.7.0'
    container_name: kafka-petshop
    hostname: kafka
    ports:
      - '9094:9094'
    environment:
      - KAFKA_CFG_NODE_ID=0
      - KAFKA_CFG_PROCESS_ROLES=controller,broker
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@kafka:9093
      - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093,EXTERNAL://:9094
      - <PERSON><PERSON><PERSON>_CFG_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092,EXTERNAL://localhost:9094
      - <PERSON><PERSON><PERSON>_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,EXTERNAL:PLAINTEXT,PLAINTEXT:PLAINTEXT
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
    networks:
      - network

  redis:
    image: redis:7.0
    container_name: redis-petshop
    restart: always
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - network
    command: ["redis-server", "--appendonly", "yes"]  # Enable AOF persistence for Redis

  mysql:
    image: mysql:8.0
    container_name: mysql-petshop
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: "root"
      MYSQL_DATABASE: "identity_service"
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./custom.cnf:/etc/mysql/conf.d/custom.cnf
    networks:
      - network

networks:
  network:

volumes:
  mysql_data: {}
  redis_data: {}
