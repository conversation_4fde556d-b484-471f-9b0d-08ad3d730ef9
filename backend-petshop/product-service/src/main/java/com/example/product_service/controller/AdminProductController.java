package com.example.product_service.controller;

import com.example.product_service.dto.request.FilterRequest;
import com.example.product_service.dto.request.ProductRequest;
import com.example.product_service.dto.response.ApiResponse;
import com.example.product_service.dto.response.PageResponse;
import com.example.product_service.dto.response.ProductResponse;
import com.example.product_service.exception.AppException;
import com.example.product_service.exception.ErrorCode;
import com.example.product_service.service.ProductService;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/admin")
@PreAuthorize("hasAuthority('MANAGE_PRODUCT')")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001"}, allowCredentials = "true")
public class AdminProductController {
    ProductService productService;

    // Get all products
    @GetMapping
    public ApiResponse<PageResponse<ProductResponse>> getAllProducts(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        validatePaginationParams(page, size);
        PageResponse<ProductResponse> products = productService.getAllProducts(page, size);
        return ApiResponse.<PageResponse<ProductResponse>>builder()
                .code(0)
                .message("Success")
                .data(products)
                .build();
    }

    // Get product by ID
    @GetMapping("/{id}")
    public ApiResponse<ProductResponse> getProductById(@PathVariable Long id) {
        ProductResponse product = productService.getProductById(id);
        return ApiResponse.<ProductResponse>builder()
                .code(0)
                .message("Success")
                .data(product)
                .build();
    }

    // Get products by category ID
    @GetMapping("/category/{categoryId}")
    public ApiResponse<PageResponse<ProductResponse>> getProductsByCategoryId(
            @PathVariable Long categoryId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        validatePaginationParams(page, size);
        PageResponse<ProductResponse> products = productService.getProductsByCategory(categoryId, page, size);
        return ApiResponse.<PageResponse<ProductResponse>>builder()
                .code(0)
                .message("Success")
                .data(products)
                .build();
    }

    // Create product
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiResponse<ProductResponse> createProduct(
            @RequestPart("request") @Valid ProductRequest request,
            @RequestPart(value = "files", required = false) MultipartFile[] files) {
        validateFiles(files);
        ProductResponse response = productService.createProduct(request, files);
        return ApiResponse.<ProductResponse>builder()
                .code(0)
                .message("Product created successfully")
                .data(response)
                .build();
    }

    // Update product
    @PutMapping(value = "/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiResponse<ProductResponse> updateProduct(
            @PathVariable Long id,
            @RequestPart("request") @Valid ProductRequest request,
            @RequestPart(value = "files", required = false) MultipartFile[] files) {
        validateFiles(files);
        ProductResponse product = productService.updateProduct(id, request, files);
        return ApiResponse.<ProductResponse>builder()
                .code(0)
                .message("Product updated successfully")
                .data(product)
                .build();
    }

    // Delete product
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteProduct(@PathVariable Long id) {
        productService.deleteProduct(id);
        return ApiResponse.<Void>builder()
                .code(0)
                .message("Sản phẩm đã được xóa (chuyển sang trạng thái không hoạt động)")
                .build();
    }

    // Restore product
    @PostMapping("/{id}/restore")
    public ApiResponse<Void> restoreProduct(@PathVariable Long id) {
        productService.restoreProduct(id);
        return ApiResponse.<Void>builder()
                .code(0)
                .message("Sản phẩm đã được khôi phục")
                .build();
    }

    // Search products
    @GetMapping("/search")
    public ApiResponse<PageResponse<ProductResponse>> searchProducts(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        validatePaginationParams(page, size);
        PageResponse<ProductResponse> products = productService.searchProducts(keyword, page, size);
        return ApiResponse.<PageResponse<ProductResponse>>builder()
                .code(0)
                .message("Success")
                .data(products)
                .build();
    }

    // Filter products
    @GetMapping("/filter")
    public ApiResponse<PageResponse<ProductResponse>> filterProducts(
            @Valid FilterRequest filter,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        validatePaginationParams(page, size);
        PageResponse<ProductResponse> products = productService.getProductsByFilter(filter, page, size);
        return ApiResponse.<PageResponse<ProductResponse>>builder()
                .code(0)
                .message("Success")
                .data(products)
                .build();
    }

    // Validation helper for pagination parameters
    private void validatePaginationParams(Integer page, Integer size) {
        if (page < 1) {
            throw new AppException(ErrorCode.INVALID_PAGINATION);
        }
        if (size < 1) {
            throw new AppException(ErrorCode.INVALID_PAGINATION);
        }
    }

    // Validation helper for files
    private void validateFiles(MultipartFile[] files) {
        if (files != null) {
            for (MultipartFile file : files) {
                if (file != null && !file.isEmpty()) {
                    String contentType = file.getContentType();
                    if (contentType == null || !contentType.startsWith("image/")) {
                        throw new AppException(ErrorCode.INVALID_FILE_TYPE);
                    }
                    if (file.getSize() > 5 * 1024 * 1024) { // 5MB limit
                        throw new AppException(ErrorCode.INVALID_FILE_SIZE);
                    }
                }
            }
        }
    }
}