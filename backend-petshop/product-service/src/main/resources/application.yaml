product:
  image:
    url-prefix: http://localhost:8082/products/image-product/
server:
  port: 8082
  servlet:
    context-path: /products
spring:
  kafka:
    bootstrap-servers: localhost:9094
    consumer:
      group-id: notification-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "*"
  web:
    resources:
      static-locations: classpath:/static/
  datasource:
    url: "***************************************************"
    username: "root"
    password: ""
  jpa:
    hibernate:
      ddl-auto: update
  cloud:
    config:
      enabled: false
      import-check:
        enabled: false
logging:
  level:
    org.springframework.web: DEBUG
    org.hibernate.validator: DEBUG

