server:
  port: 8081
  servlet:
    context-path: /profiles

spring:
  kafka:
    bootstrap-servers: localhost:9094
    consumer:
      group-id: notification-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "*"
  datasource:
    url: "***************************************************"
    username: "root"
    password: "root"
  jpa:
    hibernate:
      ddl-auto: update
logging:
  level:
    org.springframework.web: DEBUG
    org.hibernate.validator: DEBUG

