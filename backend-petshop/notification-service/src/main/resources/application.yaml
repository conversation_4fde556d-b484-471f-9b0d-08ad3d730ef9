app:
  otp:
    expiration: 30
  mail:
    brevo:
      url: "https://api.brevo.com/v3/smtp/email"
      api-key: "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"
      name-sender: "Nong San Viet Nam"
      email-sender: "<EMAIL>"
  phone:
    twilio:
      service-sid: "VA0f778faf01b9fbc35b2be7e1ca9d9ebe"
      account-sid: "**********************************"
      auth-token: "956aca85cb752df16acab231cac358fa"
      phone-number: "+***********"
server:
  port: 8085
  servlet:
    context-path: /notifications
spring:
  kafka:
    bootstrap-servers: localhost:9094
    consumer:
      group-id: notification-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "*"
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
  cloud:
    config:
      enabled: false
      import-check:
        enabled: false
  application:
    name: notification-service
  data:
    mongodb:
      uri: *************************************************************************