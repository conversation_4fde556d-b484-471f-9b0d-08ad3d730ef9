server:
  port: 8888

app:
  api-prefix: /api/v1
spring:
  application:
    name: api_gateway
  cloud:
    gateway:
      routes:
        - id: identity_service
          uri: http://localhost:8080
          predicates:
            - Path=${app.api-prefix}/identity/**
          filters:
            - StripPrefix=2
        - id: profile_service
          uri: http://localhost:8081
          predicates:
            - Path=${app.api-prefix}/profiles/**
          filters:
            - StripPrefix=2
        - id: product_service
          uri: http://localhost:8082
          predicates:
            - Path=${app.api-prefix}/products/**
          filters:
            - StripPrefix=2
        - id: review_service
          uri: http://localhost:8083
          predicates:
            - Path=${app.api-prefix}/reviews/**
          filters:
            - StripPrefix=2
        - id: notifications_service
          uri: http://localhost:8085
          predicates:
            - Path=${app.api-prefix}/notifications/**
          filters:
            - StripPrefix=2
        - id: order_service
          uri: http://localhost:8086
          predicates:
            - Path=${app.api-prefix}/orders/**
          filters:
            - StripPrefix=2
        - id: shipping_service
          uri: http://localhost:8087
          predicates:
            - Path=${app.api-prefix}/shipping/**
          filters:
            - StripPrefix=2
        - id: payment_service
          uri: http://localhost:8088
          predicates:
            - Path=${app.api-prefix}/payment/**
          filters:
            - StripPrefix=2