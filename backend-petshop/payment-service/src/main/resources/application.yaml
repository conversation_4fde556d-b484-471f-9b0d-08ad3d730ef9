Frontend_Payment_Return: "http://localhost:3000/payment-result"
vnPay:
#  tmn_code: "0FF15K06"
#  hash_secret: "8MI4UR0TM6NZXMVDE91LWU9GSC4YEO6O"
#  url: "https://sandbox.vnpayment.vn/paymentv2/vpcpay.html"
#  return_url: "http://localhost:8088/payment/vnpay_return"
  PAY_URL: "https://sandbox.vnpayment.vn/paymentv2/vpcpay.html"
  TMN_CODE: "0FF15K06"
  SECRET_KEY: "8MI4UR0TM6NZXMVDE91LWU9GSC4YEO6O"
  RETURN_URL: "http://localhost:8088/payment/vnpay/pay-callback"
  VERSION: "2.1.0"
  COMMAND: "pay"
  ORDER_TYPE: "other"
logging:
  level:
    root: DEBUG
server:
  port: 8088
  servlet:
    context-path: /payment

spring:
  application:
    name: payment-service
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
  data:
    mongodb:
      uri: ********************************************************************
