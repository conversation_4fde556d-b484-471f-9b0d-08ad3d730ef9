server:
  port: 8086
  servlet:
    context-path: /orders
spring:
  kafka:
    bootstrap-servers: localhost:9094
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
  datasource:
    url: "*************************************************"
    username: "root"
    password: "root"
  jpa:
    hibernate:
      ddl-auto: update
logging:
  level:
    org.springframework.web: DEBUG
    org.hibernate.validator: DEBUG

