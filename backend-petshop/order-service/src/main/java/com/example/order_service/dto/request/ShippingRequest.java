package com.example.order_service.dto.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ShippingRequest {
    @NotNull(message = "<PERSON>h sách sản phẩm không được để trống")
    @Size(min = 1, message = "<PERSON><PERSON><PERSON> có ít nhất một sản phẩm")
    private List<OrderItemRequest> products;
    private OrderRequest order;
}
